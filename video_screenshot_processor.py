#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频截图处理脚本
从视频中按时间戳截取图片，并添加黄底白字的中文文字
"""

import cv2
import os
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def time_to_seconds(time_str):
    """将时间字符串转换为秒数"""
    if ':' in time_str:
        parts = time_str.split(':')
        if len(parts) == 2:  # MM:SS
            minutes, seconds = map(float, parts)
            return minutes * 60 + seconds
        elif len(parts) == 3:  # HH:MM:SS
            hours, minutes, seconds = map(float, parts)
            return hours * 3600 + minutes * 60 + seconds
    else:
        return float(time_str)  # 直接是秒数

def extract_frame_at_timestamp(video_path, timestamp_seconds):
    """从视频中提取指定时间戳的帧"""
    cap = cv2.VideoCapture(video_path)
    
    # 设置视频位置到指定时间戳
    cap.set(cv2.CAP_PROP_POS_MSEC, timestamp_seconds * 1000)
    
    # 读取帧
    ret, frame = cap.read()
    cap.release()
    
    if ret:
        # 将BGR转换为RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        return Image.fromarray(frame_rgb)
    else:
        return None

def add_text_to_image(image, text, font_path, font_size=36):
    """在图片左上角添加黄底白字的文字"""
    # 创建可编辑的图片副本
    img_with_text = image.copy()
    draw = ImageDraw.Draw(img_with_text)
    
    try:
        # 加载字体
        font = ImageFont.truetype(font_path, font_size)
    except:
        print(f"无法加载字体 {font_path}，使用默认字体")
        font = ImageFont.load_default()
    
    # 获取文字尺寸
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    # 设置文字位置（左上角，距离边框一定距离）
    margin = 20
    x = margin
    y = margin
    
    # 绘制黄色背景矩形
    padding = 10
    rect_coords = [
        x - padding, 
        y - padding, 
        x + text_width + padding, 
        y + text_height + padding
    ]
    draw.rectangle(rect_coords, fill='yellow', outline='orange', width=2)
    
    # 绘制白色文字
    draw.text((x, y), text, fill='white', font=font)
    
    return img_with_text

def process_video_screenshots(video_path, timestamps_and_texts, font_path, output_dir):
    """处理视频截图并添加文字"""
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"开始处理视频: {video_path}")
    print(f"输出目录: {output_dir}")
    print(f"字体路径: {font_path}")
    
    for i, (timestamp, text) in enumerate(timestamps_and_texts):
        print(f"\n处理第 {i+1}/{len(timestamps_and_texts)} 个截图...")
        print(f"时间戳: {timestamp}, 文字: {text}")
        
        # 转换时间戳为秒数
        seconds = time_to_seconds(timestamp)
        
        # 提取帧
        frame = extract_frame_at_timestamp(video_path, seconds)
        
        if frame is None:
            print(f"无法提取时间戳 {timestamp} 的帧")
            continue
        
        # 添加文字
        frame_with_text = add_text_to_image(frame, text, font_path)
        
        # 保存图片
        output_filename = f"screenshot_{i+1:03d}_{timestamp.replace(':', '-')}.png"
        output_path = os.path.join(output_dir, output_filename)
        frame_with_text.save(output_path, 'PNG')
        
        print(f"已保存: {output_filename}")
    
    print(f"\n处理完成！共处理了 {len(timestamps_and_texts)} 个截图")

def main():
    # 配置参数
    video_path = r"C:\Users\<USER>\Downloads\Video\Introducing the first agent for large-scale software development.mp4"
    font_path = r"E:\Font\SmileySans-Oblique.otf"
    output_dir = r"E:\Augment\demo"
    
    # 时间戳和对应文字列表
    # 格式: (时间戳, 文字内容)
    # 时间戳可以是 "MM:SS" 或 "HH:MM:SS" 格式，或直接是秒数
    timestamps_and_texts = [
        # 请在这里添加您的时间戳和对应文字
        # 示例:
        # ("0:05", "介绍开始"),
        # ("0:30", "产品展示"),
        # ("1:15", "功能演示"),
        # ("2:00", "技术特点"),
        # ("3:30", "使用案例"),
        # ("4:45", "总结"),
        
        # 由于您没有提供具体的时间戳，我添加了一些示例
        # 请根据您的需求修改这些时间戳和文字
        ("0:00", "Augment Agent 介绍"),
        ("0:10", "大规模软件开发"),
        ("0:20", "AI编程助手"),
        ("0:30", "智能代码理解"),
        ("0:40", "团队协作功能"),
        ("0:50", "企业级安全"),
    ]
    
    # 检查文件是否存在
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在: {video_path}")
        return
    
    if not os.path.exists(font_path):
        print(f"警告: 字体文件不存在: {font_path}")
        print("将使用默认字体")
    
    # 处理视频截图
    process_video_screenshots(video_path, timestamps_and_texts, font_path, output_dir)

if __name__ == "__main__":
    main()
