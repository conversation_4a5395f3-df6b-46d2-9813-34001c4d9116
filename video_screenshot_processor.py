#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频截图处理脚本
从视频中按时间戳截取图片，并添加黄底白字的中文文字
"""

import cv2
import os
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def time_to_seconds(time_str):
    """将时间字符串转换为秒数"""
    if ':' in time_str:
        parts = time_str.split(':')
        if len(parts) == 2:  # MM:SS
            minutes, seconds = map(float, parts)
            return minutes * 60 + seconds
        elif len(parts) == 3:  # HH:MM:SS
            hours, minutes, seconds = map(float, parts)
            return hours * 3600 + minutes * 60 + seconds
    else:
        return float(time_str)  # 直接是秒数

def extract_frame_at_timestamp(video_path, timestamp_seconds, target_width=1920):
    """从视频中提取指定时间戳的高清帧并放大到指定尺寸"""
    cap = cv2.VideoCapture(video_path)

    # 获取视频的原始分辨率
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    print(f"  原始视频分辨率: {width}x{height}")

    # 设置视频位置到指定时间戳
    cap.set(cv2.CAP_PROP_POS_MSEC, timestamp_seconds * 1000)

    # 读取帧
    ret, frame = cap.read()
    cap.release()

    if ret:
        # 将BGR转换为RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        # 转换为PIL图像
        pil_image = Image.fromarray(frame_rgb)

        # 计算放大比例，保持宽高比
        scale_factor = target_width / width
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)

        # 使用高质量重采样放大图片
        pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        print(f"  放大后分辨率: {new_width}x{new_height}")

        return pil_image
    else:
        return None

def add_text_to_image(image, text, font_path, font_size=48):
    """在图片顶部添加黄色横条背景和白色中文字体，类似PPT效果"""
    # 创建可编辑的图片副本
    img_with_text = image.copy()
    img_width, img_height = img_with_text.size
    draw = ImageDraw.Draw(img_with_text)

    try:
        # 加载字体，使用更大的字号
        font = ImageFont.truetype(font_path, font_size)
        print(f"  成功加载字体: {font_path}")
    except Exception as e:
        print(f"  无法加载字体 {font_path}: {e}")
        print("  使用默认字体")
        font = ImageFont.load_default()

    # 获取文字尺寸
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]

    # 设置黄色横条的高度（根据文字高度调整）
    bar_height = max(80, text_height + 40)  # 至少80像素高

    # 绘制顶部黄色横条背景（覆盖整个图片宽度）
    yellow_color = '#FFD700'  # 金黄色
    draw.rectangle([0, 0, img_width, bar_height], fill=yellow_color)

    # 计算文字居中位置
    text_x = (img_width - text_width) // 2  # 水平居中
    text_y = (bar_height - text_height) // 2  # 在黄色条中垂直居中

    # 绘制白色文字
    draw.text((text_x, text_y), text, fill='white', font=font)

    # 可选：添加阴影效果
    shadow_offset = 2
    draw.text((text_x + shadow_offset, text_y + shadow_offset), text, fill='#333333', font=font)
    draw.text((text_x, text_y), text, fill='white', font=font)

    return img_with_text

def process_video_screenshots(video_path, timestamps_and_texts, font_path, output_dir):
    """处理视频截图并添加文字"""
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"开始处理视频: {video_path}")
    print(f"输出目录: {output_dir}")
    print(f"字体路径: {font_path}")
    
    for i, (timestamp, text) in enumerate(timestamps_and_texts):
        print(f"\n处理第 {i+1}/{len(timestamps_and_texts)} 个截图...")
        print(f"时间戳: {timestamp}, 文字: {text}")
        
        # 转换时间戳为秒数
        seconds = time_to_seconds(timestamp)
        
        # 提取帧
        frame = extract_frame_at_timestamp(video_path, seconds)
        
        if frame is None:
            print(f"无法提取时间戳 {timestamp} 的帧")
            continue
        
        # 添加文字
        frame_with_text = add_text_to_image(frame, text, font_path)
        
        # 保存图片（高质量PNG格式）
        safe_text = text.replace(':', '').replace('/', '').replace('\\', '').replace('?', '').replace('*', '')
        output_filename = f"{timestamp.replace(':', '-')}_{safe_text}.png"
        output_path = os.path.join(output_dir, output_filename)

        # 保存为高质量PNG
        frame_with_text.save(output_path, 'PNG', quality=100, optimize=False)

        print(f"已保存: {output_filename}")
        print(f"  图片尺寸: {frame_with_text.size[0]}x{frame_with_text.size[1]}")
    
    print(f"\n处理完成！共处理了 {len(timestamps_and_texts)} 个截图")

def main():
    # 配置参数
    video_path = r"C:\Users\<USER>\Downloads\Video\Introducing the first agent for large-scale software development.mp4"
    font_path = r"E:\Font\SmileySans-Oblique.otf"
    output_dir = r"E:\Augment\demo"
    
    # 时间戳和对应文字列表
    # 格式: (时间戳, 文字内容)
    timestamps_and_texts = [
        ("2:22", "多个代理并行工作"),
        ("3:11", "演示：从 issue 到 pull request"),
        ("3:23", "从 Issue 创建实施计划"),
        ("3:36", "执行实施计划"),
        ("3:47", "终端操作与自我纠错"),
        ("4:02", "基于上下文创建 UI 组件"),
        ("4:25", "完成复杂功能"),
        ("5:27", "智能 Git 提交"),
        ("5:51", "创建 Pull Request"),
        ("6:48", "VSCode and JetBrains"),
        ("7:24", "深度上下文和工具"),
    ]
    
    # 检查文件是否存在
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在: {video_path}")
        return
    
    if not os.path.exists(font_path):
        print(f"警告: 字体文件不存在: {font_path}")
        print("将使用默认字体")
    
    # 处理视频截图
    process_video_screenshots(video_path, timestamps_and_texts, font_path, output_dir)

if __name__ == "__main__":
    main()
