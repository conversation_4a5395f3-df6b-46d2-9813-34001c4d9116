<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augment Agent 集成指南</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .content-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .intro-section {
            margin-bottom: 40px;
        }

        .intro-section h2 {
            color: #4a5568;
            font-size: 2rem;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .intro-section p {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 15px;
        }

        .setup-steps {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }

        .setup-steps h3 {
            font-size: 1.5rem;
            margin-bottom: 20px;
        }

        .setup-steps ol {
            margin-left: 20px;
        }

        .setup-steps li {
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .integrations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .integration-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .integration-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        }

        .integration-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .integration-card .icon {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 10px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .integration-card p {
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .examples {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .examples h4 {
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .examples ul {
            list-style: none;
        }

        .examples li {
            background: rgba(255,255,255,0.1);
            margin-bottom: 8px;
            padding: 10px 15px;
            border-radius: 8px;
            border-left: 4px solid rgba(255,255,255,0.3);
        }

        .examples li:before {
            content: "💡 ";
            margin-right: 8px;
        }

        .special-note {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #ff6b6b;
        }

        .special-note h4 {
            color: #d63031;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .special-note p {
            color: #2d3436;
            margin-bottom: 10px;
        }

        .next-steps {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 30px;
            margin-top: 40px;
            text-align: center;
        }

        .next-steps h3 {
            color: #2d3436;
            font-size: 1.8rem;
            margin-bottom: 20px;
        }

        .next-steps a {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 25px;
            margin: 10px;
            transition: transform 0.3s ease;
            font-weight: 600;
        }

        .next-steps a:hover {
            transform: scale(1.05);
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .content-card {
                padding: 20px;
            }
            
            .integrations-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Augment Agent 集成指南</h1>
            <p>配置外部服务集成，让AI助手访问GitHub、Linear、Jira、Confluence和Notion</p>
        </div>

        <div class="content-card">
            <div class="intro-section">
                <h2>📋 关于Agent集成</h2>
                <p>Augment Agent可以通过集成访问外部服务，为您的请求添加额外的上下文信息，并代表您执行操作。这些集成允许Augment Agent无缝地与您的开发工具协作，无需离开编辑器。</p>
                <p>设置完成后，Augment Agent将根据您的请求上下文自动使用适当的集成。或者，您也可以在请求中提及服务名称来使用特定的集成。</p>
            </div>

            <div class="setup-steps">
                <h3>⚙️ 设置集成步骤</h3>
                <p>在VS Code中为Augment Agent设置集成，请按照以下步骤操作：</p>
                <ol>
                    <li>点击Augment聊天窗口右上角的设置图标，或按Cmd/Ctrl Shift P并选择"显示设置面板"</li>
                    <li>点击您想要设置的集成旁边的"连接"按钮</li>
                </ol>
                <p>您将被重定向到相应服务的授权页面。授权完成后，该集成将可用于Augment Agent。</p>
            </div>
        </div>

        <div class="integrations-grid">
            <div class="integration-card">
                <h3><span class="icon">🐙</span>GitHub 集成</h3>
                <p>为您的请求添加额外的上下文信息并执行操作。从GitHub Issue中提取信息，对代码进行更改（或让Agent为您完成），并打开Pull Request，所有这些都无需离开编辑器。</p>
                <div class="examples">
                    <h4>🌟 使用示例：</h4>
                    <ul>
                        <li>"实现Issue #123并打开一个pull request"</li>
                        <li>"查找所有分配给我的issues"</li>
                        <li>"检查我最新提交的CI状态"</li>
                    </ul>
                </div>
            </div>

            <div class="integration-card">
                <h3><span class="icon">📏</span>Linear 集成</h3>
                <p>在您的IDE中读取、更新、评论和解决Linear问题。</p>
                <div class="examples">
                    <h4>🌟 使用示例：</h4>
                    <ul>
                        <li>"修复TES-1"</li>
                        <li>"为这些TODO创建Linear票据"</li>
                        <li>"帮我分类这些新的bug报告"</li>
                    </ul>
                </div>
            </div>

            <div class="integration-card">
                <h3><span class="icon">🎯</span>Jira 集成</h3>
                <p>处理您的Jira问题，创建新票据，并更新现有票据。</p>
                <div class="examples">
                    <h4>🌟 使用示例：</h4>
                    <ul>
                        <li>"显示所有分配给我的Jira票据"</li>
                        <li>"为这个bug创建一个Jira票据"</li>
                        <li>"创建PR来修复SOF-123"</li>
                        <li>"将PROJ-123的状态更新为'进行中'"</li>
                    </ul>
                </div>
            </div>

            <div class="integration-card">
                <h3><span class="icon">📚</span>Confluence 集成</h3>
                <p>直接从您的IDE查询现有文档或更新页面。确保团队的知识库保持最新，无需任何上下文切换。</p>
                <div class="examples">
                    <h4>🌟 使用示例：</h4>
                    <ul>
                        <li>"总结我们关于微服务架构的Confluence页面"</li>
                        <li>"在Confluence中查找关于我们发布流程的信息"</li>
                        <li>"更新我们的入职文档，解释我们如何使用Bazel"</li>
                    </ul>
                </div>
            </div>

            <div class="integration-card">
                <h3><span class="icon">📝</span>Notion 集成</h3>
                <p>从团队的知识库中搜索和检索信息 - 访问文档、会议记录和项目规范。此集成目前为只读模式。</p>
                <div class="examples">
                    <h4>🌟 使用示例：</h4>
                    <ul>
                        <li>"查找关于我们API文档的Notion页面"</li>
                        <li>"显示支付系统的技术规范"</li>
                        <li>"昨天团队会议中还有哪些未完成的任务？"</li>
                    </ul>
                </div>
            </div>

            <div class="integration-card">
                <h3><span class="icon">🔍</span>Glean 集成</h3>
                <p>利用Glean强大的搜索引擎，让agent从团队的内部数据源中检索信息。</p>
                <div class="special-note">
                    <h4>⚠️ 特别说明</h4>
                    <p>Glean集成目前处于早期访问阶段，与其他集成略有不同：</p>
                    <p>• 目前仅对企业客户开放</p>
                    <p>• 不会出现在设置面板的集成列表中</p>
                    <p>• 需要Augment和Glean的管理员权限才能启用</p>
                </div>
                <div class="examples">
                    <h4>🌟 使用示例：</h4>
                    <ul>
                        <li>"在Glean中搜索过去的相关事件及其解决方案"</li>
                        <li>"在Glean中搜索我们迁移到新支付处理器的原因"</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="next-steps">
            <h3>🎯 下一步操作</h3>
            <a href="#mcp">配置MCP服务器</a>
            <a href="#chat">使用聊天功能</a>
        </div>
    </div>
</body>
</html>
